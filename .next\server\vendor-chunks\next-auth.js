"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next-auth";
exports.ids = ["vendor-chunks/next-auth"];
exports.modules = {

/***/ "(ssr)/./node_modules/next-auth/client/_utils.js":
/*!*************************************************!*\
  !*** ./node_modules/next-auth/client/_utils.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(ssr)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.BroadcastChannel = BroadcastChannel;\nexports.apiBaseUrl = apiBaseUrl;\nexports.fetchData = fetchData;\nexports.now = now;\nvar _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ \"(ssr)/./node_modules/@babel/runtime/regenerator/index.js\"));\nvar _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/defineProperty.js\"));\nvar _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ \"(ssr)/./node_modules/@babel/runtime/helpers/asyncToGenerator.js\"));\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2.default)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction fetchData(_x, _x2, _x3) {\n  return _fetchData.apply(this, arguments);\n}\nfunction _fetchData() {\n  _fetchData = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee(path, __NEXTAUTH, logger) {\n    var _ref,\n      ctx,\n      _ref$req,\n      req,\n      url,\n      _req$headers,\n      options,\n      res,\n      data,\n      _args = arguments;\n    return _regenerator.default.wrap(function _callee$(_context) {\n      while (1) switch (_context.prev = _context.next) {\n        case 0:\n          _ref = _args.length > 3 && _args[3] !== undefined ? _args[3] : {}, ctx = _ref.ctx, _ref$req = _ref.req, req = _ref$req === void 0 ? ctx === null || ctx === void 0 ? void 0 : ctx.req : _ref$req;\n          url = \"\".concat(apiBaseUrl(__NEXTAUTH), \"/\").concat(path);\n          _context.prev = 2;\n          options = {\n            headers: _objectSpread({\n              \"Content-Type\": \"application/json\"\n            }, req !== null && req !== void 0 && (_req$headers = req.headers) !== null && _req$headers !== void 0 && _req$headers.cookie ? {\n              cookie: req.headers.cookie\n            } : {})\n          };\n          if (req !== null && req !== void 0 && req.body) {\n            options.body = JSON.stringify(req.body);\n            options.method = \"POST\";\n          }\n          _context.next = 7;\n          return fetch(url, options);\n        case 7:\n          res = _context.sent;\n          _context.next = 10;\n          return res.json();\n        case 10:\n          data = _context.sent;\n          if (res.ok) {\n            _context.next = 13;\n            break;\n          }\n          throw data;\n        case 13:\n          return _context.abrupt(\"return\", Object.keys(data).length > 0 ? data : null);\n        case 16:\n          _context.prev = 16;\n          _context.t0 = _context[\"catch\"](2);\n          logger.error(\"CLIENT_FETCH_ERROR\", {\n            error: _context.t0,\n            url: url\n          });\n          return _context.abrupt(\"return\", null);\n        case 20:\n        case \"end\":\n          return _context.stop();\n      }\n    }, _callee, null, [[2, 16]]);\n  }));\n  return _fetchData.apply(this, arguments);\n}\nfunction apiBaseUrl(__NEXTAUTH) {\n  if (typeof window === \"undefined\") {\n    return \"\".concat(__NEXTAUTH.baseUrlServer).concat(__NEXTAUTH.basePathServer);\n  }\n  return __NEXTAUTH.basePath;\n}\nfunction now() {\n  return Math.floor(Date.now() / 1000);\n}\nfunction BroadcastChannel() {\n  var name = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : \"nextauth.message\";\n  return {\n    receive: function receive(onReceive) {\n      var handler = function handler(event) {\n        var _event$newValue;\n        if (event.key !== name) return;\n        var message = JSON.parse((_event$newValue = event.newValue) !== null && _event$newValue !== void 0 ? _event$newValue : \"{}\");\n        if ((message === null || message === void 0 ? void 0 : message.event) !== \"session\" || !(message !== null && message !== void 0 && message.data)) return;\n        onReceive(message);\n      };\n      window.addEventListener(\"storage\", handler);\n      return function () {\n        return window.removeEventListener(\"storage\", handler);\n      };\n    },\n    post: function post(message) {\n      if (typeof window === \"undefined\") return;\n      try {\n        localStorage.setItem(name, JSON.stringify(_objectSpread(_objectSpread({}, message), {}, {\n          timestamp: now()\n        })));\n      } catch (_unused) {}\n    }\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-auth/client/_utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-auth/core/errors.js":
/*!***********************************************!*\
  !*** ./node_modules/next-auth/core/errors.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(ssr)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.UnsupportedStrategy = exports.UnknownError = exports.OAuthCallbackError = exports.MissingSecret = exports.MissingAuthorize = exports.MissingAdapterMethods = exports.MissingAdapter = exports.MissingAPIRoute = exports.InvalidCallbackUrl = exports.AccountNotLinkedError = void 0;\nexports.adapterErrorHandler = adapterErrorHandler;\nexports.capitalize = capitalize;\nexports.eventsErrorHandler = eventsErrorHandler;\nexports.upperSnake = upperSnake;\nvar _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ \"(ssr)/./node_modules/@babel/runtime/regenerator/index.js\"));\nvar _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ \"(ssr)/./node_modules/@babel/runtime/helpers/asyncToGenerator.js\"));\nvar _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/defineProperty.js\"));\nvar _classCallCheck2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/classCallCheck */ \"(ssr)/./node_modules/@babel/runtime/helpers/classCallCheck.js\"));\nvar _createClass2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/createClass */ \"(ssr)/./node_modules/@babel/runtime/helpers/createClass.js\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/possibleConstructorReturn */ \"(ssr)/./node_modules/@babel/runtime/helpers/possibleConstructorReturn.js\"));\nvar _getPrototypeOf2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/getPrototypeOf */ \"(ssr)/./node_modules/@babel/runtime/helpers/getPrototypeOf.js\"));\nvar _inherits2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/inherits */ \"(ssr)/./node_modules/@babel/runtime/helpers/inherits.js\"));\nvar _wrapNativeSuper2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/wrapNativeSuper */ \"(ssr)/./node_modules/@babel/runtime/helpers/wrapNativeSuper.js\"));\nfunction _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nvar UnknownError = exports.UnknownError = function (_Error) {\n  function UnknownError(error) {\n    var _message;\n    var _this;\n    (0, _classCallCheck2.default)(this, UnknownError);\n    _this = _callSuper(this, UnknownError, [(_message = error === null || error === void 0 ? void 0 : error.message) !== null && _message !== void 0 ? _message : error]);\n    _this.name = \"UnknownError\";\n    _this.code = error.code;\n    if (error instanceof Error) {\n      _this.stack = error.stack;\n    }\n    return _this;\n  }\n  (0, _inherits2.default)(UnknownError, _Error);\n  return (0, _createClass2.default)(UnknownError, [{\n    key: \"toJSON\",\n    value: function toJSON() {\n      return {\n        name: this.name,\n        message: this.message,\n        stack: this.stack\n      };\n    }\n  }]);\n}((0, _wrapNativeSuper2.default)(Error));\nvar OAuthCallbackError = exports.OAuthCallbackError = function (_UnknownError) {\n  function OAuthCallbackError() {\n    var _this2;\n    (0, _classCallCheck2.default)(this, OAuthCallbackError);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this2 = _callSuper(this, OAuthCallbackError, [].concat(args));\n    (0, _defineProperty2.default)(_this2, \"name\", \"OAuthCallbackError\");\n    return _this2;\n  }\n  (0, _inherits2.default)(OAuthCallbackError, _UnknownError);\n  return (0, _createClass2.default)(OAuthCallbackError);\n}(UnknownError);\nvar AccountNotLinkedError = exports.AccountNotLinkedError = function (_UnknownError2) {\n  function AccountNotLinkedError() {\n    var _this3;\n    (0, _classCallCheck2.default)(this, AccountNotLinkedError);\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    _this3 = _callSuper(this, AccountNotLinkedError, [].concat(args));\n    (0, _defineProperty2.default)(_this3, \"name\", \"AccountNotLinkedError\");\n    return _this3;\n  }\n  (0, _inherits2.default)(AccountNotLinkedError, _UnknownError2);\n  return (0, _createClass2.default)(AccountNotLinkedError);\n}(UnknownError);\nvar MissingAPIRoute = exports.MissingAPIRoute = function (_UnknownError3) {\n  function MissingAPIRoute() {\n    var _this4;\n    (0, _classCallCheck2.default)(this, MissingAPIRoute);\n    for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n      args[_key3] = arguments[_key3];\n    }\n    _this4 = _callSuper(this, MissingAPIRoute, [].concat(args));\n    (0, _defineProperty2.default)(_this4, \"name\", \"MissingAPIRouteError\");\n    (0, _defineProperty2.default)(_this4, \"code\", \"MISSING_NEXTAUTH_API_ROUTE_ERROR\");\n    return _this4;\n  }\n  (0, _inherits2.default)(MissingAPIRoute, _UnknownError3);\n  return (0, _createClass2.default)(MissingAPIRoute);\n}(UnknownError);\nvar MissingSecret = exports.MissingSecret = function (_UnknownError4) {\n  function MissingSecret() {\n    var _this5;\n    (0, _classCallCheck2.default)(this, MissingSecret);\n    for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n      args[_key4] = arguments[_key4];\n    }\n    _this5 = _callSuper(this, MissingSecret, [].concat(args));\n    (0, _defineProperty2.default)(_this5, \"name\", \"MissingSecretError\");\n    (0, _defineProperty2.default)(_this5, \"code\", \"NO_SECRET\");\n    return _this5;\n  }\n  (0, _inherits2.default)(MissingSecret, _UnknownError4);\n  return (0, _createClass2.default)(MissingSecret);\n}(UnknownError);\nvar MissingAuthorize = exports.MissingAuthorize = function (_UnknownError5) {\n  function MissingAuthorize() {\n    var _this6;\n    (0, _classCallCheck2.default)(this, MissingAuthorize);\n    for (var _len5 = arguments.length, args = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {\n      args[_key5] = arguments[_key5];\n    }\n    _this6 = _callSuper(this, MissingAuthorize, [].concat(args));\n    (0, _defineProperty2.default)(_this6, \"name\", \"MissingAuthorizeError\");\n    (0, _defineProperty2.default)(_this6, \"code\", \"CALLBACK_CREDENTIALS_HANDLER_ERROR\");\n    return _this6;\n  }\n  (0, _inherits2.default)(MissingAuthorize, _UnknownError5);\n  return (0, _createClass2.default)(MissingAuthorize);\n}(UnknownError);\nvar MissingAdapter = exports.MissingAdapter = function (_UnknownError6) {\n  function MissingAdapter() {\n    var _this7;\n    (0, _classCallCheck2.default)(this, MissingAdapter);\n    for (var _len6 = arguments.length, args = new Array(_len6), _key6 = 0; _key6 < _len6; _key6++) {\n      args[_key6] = arguments[_key6];\n    }\n    _this7 = _callSuper(this, MissingAdapter, [].concat(args));\n    (0, _defineProperty2.default)(_this7, \"name\", \"MissingAdapterError\");\n    (0, _defineProperty2.default)(_this7, \"code\", \"EMAIL_REQUIRES_ADAPTER_ERROR\");\n    return _this7;\n  }\n  (0, _inherits2.default)(MissingAdapter, _UnknownError6);\n  return (0, _createClass2.default)(MissingAdapter);\n}(UnknownError);\nvar MissingAdapterMethods = exports.MissingAdapterMethods = function (_UnknownError7) {\n  function MissingAdapterMethods() {\n    var _this8;\n    (0, _classCallCheck2.default)(this, MissingAdapterMethods);\n    for (var _len7 = arguments.length, args = new Array(_len7), _key7 = 0; _key7 < _len7; _key7++) {\n      args[_key7] = arguments[_key7];\n    }\n    _this8 = _callSuper(this, MissingAdapterMethods, [].concat(args));\n    (0, _defineProperty2.default)(_this8, \"name\", \"MissingAdapterMethodsError\");\n    (0, _defineProperty2.default)(_this8, \"code\", \"MISSING_ADAPTER_METHODS_ERROR\");\n    return _this8;\n  }\n  (0, _inherits2.default)(MissingAdapterMethods, _UnknownError7);\n  return (0, _createClass2.default)(MissingAdapterMethods);\n}(UnknownError);\nvar UnsupportedStrategy = exports.UnsupportedStrategy = function (_UnknownError8) {\n  function UnsupportedStrategy() {\n    var _this9;\n    (0, _classCallCheck2.default)(this, UnsupportedStrategy);\n    for (var _len8 = arguments.length, args = new Array(_len8), _key8 = 0; _key8 < _len8; _key8++) {\n      args[_key8] = arguments[_key8];\n    }\n    _this9 = _callSuper(this, UnsupportedStrategy, [].concat(args));\n    (0, _defineProperty2.default)(_this9, \"name\", \"UnsupportedStrategyError\");\n    (0, _defineProperty2.default)(_this9, \"code\", \"CALLBACK_CREDENTIALS_JWT_ERROR\");\n    return _this9;\n  }\n  (0, _inherits2.default)(UnsupportedStrategy, _UnknownError8);\n  return (0, _createClass2.default)(UnsupportedStrategy);\n}(UnknownError);\nvar InvalidCallbackUrl = exports.InvalidCallbackUrl = function (_UnknownError9) {\n  function InvalidCallbackUrl() {\n    var _this10;\n    (0, _classCallCheck2.default)(this, InvalidCallbackUrl);\n    for (var _len9 = arguments.length, args = new Array(_len9), _key9 = 0; _key9 < _len9; _key9++) {\n      args[_key9] = arguments[_key9];\n    }\n    _this10 = _callSuper(this, InvalidCallbackUrl, [].concat(args));\n    (0, _defineProperty2.default)(_this10, \"name\", \"InvalidCallbackUrl\");\n    (0, _defineProperty2.default)(_this10, \"code\", \"INVALID_CALLBACK_URL_ERROR\");\n    return _this10;\n  }\n  (0, _inherits2.default)(InvalidCallbackUrl, _UnknownError9);\n  return (0, _createClass2.default)(InvalidCallbackUrl);\n}(UnknownError);\nfunction upperSnake(s) {\n  return s.replace(/([A-Z])/g, \"_$1\").toUpperCase();\n}\nfunction capitalize(s) {\n  return \"\".concat(s[0].toUpperCase()).concat(s.slice(1));\n}\nfunction eventsErrorHandler(methods, logger) {\n  return Object.keys(methods).reduce(function (acc, name) {\n    acc[name] = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee() {\n      var method,\n        _args = arguments;\n      return _regenerator.default.wrap(function _callee$(_context) {\n        while (1) switch (_context.prev = _context.next) {\n          case 0:\n            _context.prev = 0;\n            method = methods[name];\n            _context.next = 4;\n            return method.apply(void 0, _args);\n          case 4:\n            return _context.abrupt(\"return\", _context.sent);\n          case 7:\n            _context.prev = 7;\n            _context.t0 = _context[\"catch\"](0);\n            logger.error(\"\".concat(upperSnake(name), \"_EVENT_ERROR\"), _context.t0);\n          case 10:\n          case \"end\":\n            return _context.stop();\n        }\n      }, _callee, null, [[0, 7]]);\n    }));\n    return acc;\n  }, {});\n}\nfunction adapterErrorHandler(adapter, logger) {\n  if (!adapter) return;\n  return Object.keys(adapter).reduce(function (acc, name) {\n    acc[name] = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee2() {\n      var _len10,\n        args,\n        _key10,\n        method,\n        e,\n        _args2 = arguments;\n      return _regenerator.default.wrap(function _callee2$(_context2) {\n        while (1) switch (_context2.prev = _context2.next) {\n          case 0:\n            _context2.prev = 0;\n            for (_len10 = _args2.length, args = new Array(_len10), _key10 = 0; _key10 < _len10; _key10++) {\n              args[_key10] = _args2[_key10];\n            }\n            logger.debug(\"adapter_\".concat(name), {\n              args: args\n            });\n            method = adapter[name];\n            _context2.next = 6;\n            return method.apply(void 0, args);\n          case 6:\n            return _context2.abrupt(\"return\", _context2.sent);\n          case 9:\n            _context2.prev = 9;\n            _context2.t0 = _context2[\"catch\"](0);\n            logger.error(\"adapter_error_\".concat(name), _context2.t0);\n            e = new UnknownError(_context2.t0);\n            e.name = \"\".concat(capitalize(name), \"Error\");\n            throw e;\n          case 15:\n          case \"end\":\n            return _context2.stop();\n        }\n      }, _callee2, null, [[0, 9]]);\n    }));\n    return acc;\n  }, {});\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-auth/core/errors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-auth/react/index.js":
/*!***********************************************!*\
  !*** ./node_modules/next-auth/react/index.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(ssr)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nvar _typeof = __webpack_require__(/*! @babel/runtime/helpers/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/typeof.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nvar _exportNames = {\n  SessionContext: true,\n  useSession: true,\n  getSession: true,\n  getCsrfToken: true,\n  getProviders: true,\n  signIn: true,\n  signOut: true,\n  SessionProvider: true\n};\nexports.SessionContext = void 0;\nexports.SessionProvider = SessionProvider;\nexports.getCsrfToken = getCsrfToken;\nexports.getProviders = getProviders;\nexports.getSession = getSession;\nexports.signIn = signIn;\nexports.signOut = signOut;\nexports.useSession = useSession;\nvar _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ \"(ssr)/./node_modules/@babel/runtime/regenerator/index.js\"));\nvar _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/defineProperty.js\"));\nvar _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ \"(ssr)/./node_modules/@babel/runtime/helpers/asyncToGenerator.js\"));\nvar _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/slicedToArray.js\"));\nvar React = _interopRequireWildcard(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar _logger2 = _interopRequireWildcard(__webpack_require__(/*! ../utils/logger */ \"(ssr)/./node_modules/next-auth/utils/logger.js\"));\nvar _parseUrl = _interopRequireDefault(__webpack_require__(/*! ../utils/parse-url */ \"(ssr)/./node_modules/next-auth/utils/parse-url.js\"));\nvar _utils = __webpack_require__(/*! ../client/_utils */ \"(ssr)/./node_modules/next-auth/client/_utils.js\");\nvar _jsxRuntime = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\nvar _types = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/next-auth/react/types.js\");\nObject.keys(_types).forEach(function (key) {\n  if (key === \"default\" || key === \"__esModule\") return;\n  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n  if (key in exports && exports[key] === _types[key]) return;\n  Object.defineProperty(exports, key, {\n    enumerable: true,\n    get: function get() {\n      return _types[key];\n    }\n  });\n});\nvar _process$env$NEXTAUTH, _ref, _process$env$NEXTAUTH2, _process$env$NEXTAUTH3, _React$createContext;\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != _typeof(e) && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2.default)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nvar __NEXTAUTH = {\n  baseUrl: (0, _parseUrl.default)((_process$env$NEXTAUTH = \"http://localhost:3000\") !== null && _process$env$NEXTAUTH !== void 0 ? _process$env$NEXTAUTH : process.env.VERCEL_URL).origin,\n  basePath: (0, _parseUrl.default)(\"http://localhost:3000\").path,\n  baseUrlServer: (0, _parseUrl.default)((_ref = (_process$env$NEXTAUTH2 = process.env.NEXTAUTH_URL_INTERNAL) !== null && _process$env$NEXTAUTH2 !== void 0 ? _process$env$NEXTAUTH2 : \"http://localhost:3000\") !== null && _ref !== void 0 ? _ref : process.env.VERCEL_URL).origin,\n  basePathServer: (0, _parseUrl.default)((_process$env$NEXTAUTH3 = process.env.NEXTAUTH_URL_INTERNAL) !== null && _process$env$NEXTAUTH3 !== void 0 ? _process$env$NEXTAUTH3 : \"http://localhost:3000\").path,\n  _lastSync: 0,\n  _session: undefined,\n  _getSession: function _getSession() {}\n};\nvar broadcast = (0, _utils.BroadcastChannel)();\nvar logger = (0, _logger2.proxyLogger)(_logger2.default, __NEXTAUTH.basePath);\nfunction useOnline() {\n  var _React$useState = React.useState(typeof navigator !== \"undefined\" ? navigator.onLine : false),\n    _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),\n    isOnline = _React$useState2[0],\n    setIsOnline = _React$useState2[1];\n  var setOnline = function setOnline() {\n    return setIsOnline(true);\n  };\n  var setOffline = function setOffline() {\n    return setIsOnline(false);\n  };\n  React.useEffect(function () {\n    window.addEventListener(\"online\", setOnline);\n    window.addEventListener(\"offline\", setOffline);\n    return function () {\n      window.removeEventListener(\"online\", setOnline);\n      window.removeEventListener(\"offline\", setOffline);\n    };\n  }, []);\n  return isOnline;\n}\nvar SessionContext = exports.SessionContext = (_React$createContext = React.createContext) === null || _React$createContext === void 0 ? void 0 : _React$createContext.call(React, undefined);\nfunction useSession(options) {\n  if (!SessionContext) {\n    throw new Error(\"React Context is unavailable in Server Components\");\n  }\n  var value = React.useContext(SessionContext);\n  if (!value && \"development\" !== \"production\") {\n    throw new Error(\"[next-auth]: `useSession` must be wrapped in a <SessionProvider />\");\n  }\n  var _ref2 = options !== null && options !== void 0 ? options : {},\n    required = _ref2.required,\n    onUnauthenticated = _ref2.onUnauthenticated;\n  var requiredAndNotLoading = required && value.status === \"unauthenticated\";\n  React.useEffect(function () {\n    if (requiredAndNotLoading) {\n      var url = \"/api/auth/signin?\".concat(new URLSearchParams({\n        error: \"SessionRequired\",\n        callbackUrl: window.location.href\n      }));\n      if (onUnauthenticated) onUnauthenticated();else window.location.href = url;\n    }\n  }, [requiredAndNotLoading, onUnauthenticated]);\n  if (requiredAndNotLoading) {\n    return {\n      data: value.data,\n      update: value.update,\n      status: \"loading\"\n    };\n  }\n  return value;\n}\nfunction getSession(_x) {\n  return _getSession2.apply(this, arguments);\n}\nfunction _getSession2() {\n  _getSession2 = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee3(params) {\n    var _params$broadcast;\n    var session;\n    return _regenerator.default.wrap(function _callee3$(_context3) {\n      while (1) switch (_context3.prev = _context3.next) {\n        case 0:\n          _context3.next = 2;\n          return (0, _utils.fetchData)(\"session\", __NEXTAUTH, logger, params);\n        case 2:\n          session = _context3.sent;\n          if ((_params$broadcast = params === null || params === void 0 ? void 0 : params.broadcast) !== null && _params$broadcast !== void 0 ? _params$broadcast : true) {\n            broadcast.post({\n              event: \"session\",\n              data: {\n                trigger: \"getSession\"\n              }\n            });\n          }\n          return _context3.abrupt(\"return\", session);\n        case 5:\n        case \"end\":\n          return _context3.stop();\n      }\n    }, _callee3);\n  }));\n  return _getSession2.apply(this, arguments);\n}\nfunction getCsrfToken(_x2) {\n  return _getCsrfToken.apply(this, arguments);\n}\nfunction _getCsrfToken() {\n  _getCsrfToken = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee4(params) {\n    var response;\n    return _regenerator.default.wrap(function _callee4$(_context4) {\n      while (1) switch (_context4.prev = _context4.next) {\n        case 0:\n          _context4.next = 2;\n          return (0, _utils.fetchData)(\"csrf\", __NEXTAUTH, logger, params);\n        case 2:\n          response = _context4.sent;\n          return _context4.abrupt(\"return\", response === null || response === void 0 ? void 0 : response.csrfToken);\n        case 4:\n        case \"end\":\n          return _context4.stop();\n      }\n    }, _callee4);\n  }));\n  return _getCsrfToken.apply(this, arguments);\n}\nfunction getProviders() {\n  return _getProviders.apply(this, arguments);\n}\nfunction _getProviders() {\n  _getProviders = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee5() {\n    return _regenerator.default.wrap(function _callee5$(_context5) {\n      while (1) switch (_context5.prev = _context5.next) {\n        case 0:\n          _context5.next = 2;\n          return (0, _utils.fetchData)(\"providers\", __NEXTAUTH, logger);\n        case 2:\n          return _context5.abrupt(\"return\", _context5.sent);\n        case 3:\n        case \"end\":\n          return _context5.stop();\n      }\n    }, _callee5);\n  }));\n  return _getProviders.apply(this, arguments);\n}\nfunction signIn(_x3, _x4, _x5) {\n  return _signIn.apply(this, arguments);\n}\nfunction _signIn() {\n  _signIn = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee6(provider, options, authorizationParams) {\n    var _ref5, _ref5$callbackUrl, callbackUrl, _ref5$redirect, redirect, baseUrl, providers, isCredentials, isEmail, isSupportingReturn, signInUrl, _signInUrl, res, data, _data$url, url, error;\n    return _regenerator.default.wrap(function _callee6$(_context6) {\n      while (1) switch (_context6.prev = _context6.next) {\n        case 0:\n          _ref5 = options !== null && options !== void 0 ? options : {}, _ref5$callbackUrl = _ref5.callbackUrl, callbackUrl = _ref5$callbackUrl === void 0 ? window.location.href : _ref5$callbackUrl, _ref5$redirect = _ref5.redirect, redirect = _ref5$redirect === void 0 ? true : _ref5$redirect;\n          baseUrl = (0, _utils.apiBaseUrl)(__NEXTAUTH);\n          _context6.next = 4;\n          return getProviders();\n        case 4:\n          providers = _context6.sent;\n          if (providers) {\n            _context6.next = 8;\n            break;\n          }\n          window.location.href = \"\".concat(baseUrl, \"/error\");\n          return _context6.abrupt(\"return\");\n        case 8:\n          if (!(!provider || !(provider in providers))) {\n            _context6.next = 11;\n            break;\n          }\n          window.location.href = \"\".concat(baseUrl, \"/signin?\").concat(new URLSearchParams({\n            callbackUrl: callbackUrl\n          }));\n          return _context6.abrupt(\"return\");\n        case 11:\n          isCredentials = providers[provider].type === \"credentials\";\n          isEmail = providers[provider].type === \"email\";\n          isSupportingReturn = isCredentials || isEmail;\n          signInUrl = \"\".concat(baseUrl, \"/\").concat(isCredentials ? \"callback\" : \"signin\", \"/\").concat(provider);\n          _signInUrl = \"\".concat(signInUrl).concat(authorizationParams ? \"?\".concat(new URLSearchParams(authorizationParams)) : \"\");\n          _context6.t0 = fetch;\n          _context6.t1 = _signInUrl;\n          _context6.t2 = {\n            \"Content-Type\": \"application/x-www-form-urlencoded\"\n          };\n          _context6.t3 = URLSearchParams;\n          _context6.t4 = _objectSpread;\n          _context6.t5 = _objectSpread({}, options);\n          _context6.t6 = {};\n          _context6.next = 25;\n          return getCsrfToken();\n        case 25:\n          _context6.t7 = _context6.sent;\n          _context6.t8 = callbackUrl;\n          _context6.t9 = {\n            csrfToken: _context6.t7,\n            callbackUrl: _context6.t8,\n            json: true\n          };\n          _context6.t10 = (0, _context6.t4)(_context6.t5, _context6.t6, _context6.t9);\n          _context6.t11 = new _context6.t3(_context6.t10);\n          _context6.t12 = {\n            method: \"post\",\n            headers: _context6.t2,\n            body: _context6.t11\n          };\n          _context6.next = 33;\n          return (0, _context6.t0)(_context6.t1, _context6.t12);\n        case 33:\n          res = _context6.sent;\n          _context6.next = 36;\n          return res.json();\n        case 36:\n          data = _context6.sent;\n          if (!(redirect || !isSupportingReturn)) {\n            _context6.next = 42;\n            break;\n          }\n          url = (_data$url = data.url) !== null && _data$url !== void 0 ? _data$url : callbackUrl;\n          window.location.href = url;\n          if (url.includes(\"#\")) window.location.reload();\n          return _context6.abrupt(\"return\");\n        case 42:\n          error = new URL(data.url).searchParams.get(\"error\");\n          if (!res.ok) {\n            _context6.next = 46;\n            break;\n          }\n          _context6.next = 46;\n          return __NEXTAUTH._getSession({\n            event: \"storage\"\n          });\n        case 46:\n          return _context6.abrupt(\"return\", {\n            error: error,\n            status: res.status,\n            ok: res.ok,\n            url: error ? null : data.url\n          });\n        case 47:\n        case \"end\":\n          return _context6.stop();\n      }\n    }, _callee6);\n  }));\n  return _signIn.apply(this, arguments);\n}\nfunction signOut(_x6) {\n  return _signOut.apply(this, arguments);\n}\nfunction _signOut() {\n  _signOut = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee7(options) {\n    var _options$redirect;\n    var _ref6, _ref6$callbackUrl, callbackUrl, baseUrl, fetchOptions, res, data, _data$url2, url;\n    return _regenerator.default.wrap(function _callee7$(_context7) {\n      while (1) switch (_context7.prev = _context7.next) {\n        case 0:\n          _ref6 = options !== null && options !== void 0 ? options : {}, _ref6$callbackUrl = _ref6.callbackUrl, callbackUrl = _ref6$callbackUrl === void 0 ? window.location.href : _ref6$callbackUrl;\n          baseUrl = (0, _utils.apiBaseUrl)(__NEXTAUTH);\n          _context7.t0 = {\n            \"Content-Type\": \"application/x-www-form-urlencoded\"\n          };\n          _context7.t1 = URLSearchParams;\n          _context7.next = 6;\n          return getCsrfToken();\n        case 6:\n          _context7.t2 = _context7.sent;\n          _context7.t3 = callbackUrl;\n          _context7.t4 = {\n            csrfToken: _context7.t2,\n            callbackUrl: _context7.t3,\n            json: true\n          };\n          _context7.t5 = new _context7.t1(_context7.t4);\n          fetchOptions = {\n            method: \"post\",\n            headers: _context7.t0,\n            body: _context7.t5\n          };\n          _context7.next = 13;\n          return fetch(\"\".concat(baseUrl, \"/signout\"), fetchOptions);\n        case 13:\n          res = _context7.sent;\n          _context7.next = 16;\n          return res.json();\n        case 16:\n          data = _context7.sent;\n          broadcast.post({\n            event: \"session\",\n            data: {\n              trigger: \"signout\"\n            }\n          });\n          if (!((_options$redirect = options === null || options === void 0 ? void 0 : options.redirect) !== null && _options$redirect !== void 0 ? _options$redirect : true)) {\n            _context7.next = 23;\n            break;\n          }\n          url = (_data$url2 = data.url) !== null && _data$url2 !== void 0 ? _data$url2 : callbackUrl;\n          window.location.href = url;\n          if (url.includes(\"#\")) window.location.reload();\n          return _context7.abrupt(\"return\");\n        case 23:\n          _context7.next = 25;\n          return __NEXTAUTH._getSession({\n            event: \"storage\"\n          });\n        case 25:\n          return _context7.abrupt(\"return\", data);\n        case 26:\n        case \"end\":\n          return _context7.stop();\n      }\n    }, _callee7);\n  }));\n  return _signOut.apply(this, arguments);\n}\nfunction SessionProvider(props) {\n  if (!SessionContext) {\n    throw new Error(\"React Context is unavailable in Server Components\");\n  }\n  var children = props.children,\n    basePath = props.basePath,\n    refetchInterval = props.refetchInterval,\n    refetchWhenOffline = props.refetchWhenOffline;\n  if (basePath) __NEXTAUTH.basePath = basePath;\n  var hasInitialSession = props.session !== undefined;\n  __NEXTAUTH._lastSync = hasInitialSession ? (0, _utils.now)() : 0;\n  var _React$useState3 = React.useState(function () {\n      if (hasInitialSession) __NEXTAUTH._session = props.session;\n      return props.session;\n    }),\n    _React$useState4 = (0, _slicedToArray2.default)(_React$useState3, 2),\n    session = _React$useState4[0],\n    setSession = _React$useState4[1];\n  var _React$useState5 = React.useState(!hasInitialSession),\n    _React$useState6 = (0, _slicedToArray2.default)(_React$useState5, 2),\n    loading = _React$useState6[0],\n    setLoading = _React$useState6[1];\n  React.useEffect(function () {\n    __NEXTAUTH._getSession = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee() {\n      var _ref4,\n        event,\n        storageEvent,\n        _args = arguments;\n      return _regenerator.default.wrap(function _callee$(_context) {\n        while (1) switch (_context.prev = _context.next) {\n          case 0:\n            _ref4 = _args.length > 0 && _args[0] !== undefined ? _args[0] : {}, event = _ref4.event;\n            _context.prev = 1;\n            storageEvent = event === \"storage\";\n            if (!(storageEvent || __NEXTAUTH._session === undefined)) {\n              _context.next = 10;\n              break;\n            }\n            __NEXTAUTH._lastSync = (0, _utils.now)();\n            _context.next = 7;\n            return getSession({\n              broadcast: !storageEvent\n            });\n          case 7:\n            __NEXTAUTH._session = _context.sent;\n            setSession(__NEXTAUTH._session);\n            return _context.abrupt(\"return\");\n          case 10:\n            if (!(!event || __NEXTAUTH._session === null || (0, _utils.now)() < __NEXTAUTH._lastSync)) {\n              _context.next = 12;\n              break;\n            }\n            return _context.abrupt(\"return\");\n          case 12:\n            __NEXTAUTH._lastSync = (0, _utils.now)();\n            _context.next = 15;\n            return getSession();\n          case 15:\n            __NEXTAUTH._session = _context.sent;\n            setSession(__NEXTAUTH._session);\n            _context.next = 22;\n            break;\n          case 19:\n            _context.prev = 19;\n            _context.t0 = _context[\"catch\"](1);\n            logger.error(\"CLIENT_SESSION_ERROR\", _context.t0);\n          case 22:\n            _context.prev = 22;\n            setLoading(false);\n            return _context.finish(22);\n          case 25:\n          case \"end\":\n            return _context.stop();\n        }\n      }, _callee, null, [[1, 19, 22, 25]]);\n    }));\n    __NEXTAUTH._getSession();\n    return function () {\n      __NEXTAUTH._lastSync = 0;\n      __NEXTAUTH._session = undefined;\n      __NEXTAUTH._getSession = function () {};\n    };\n  }, []);\n  React.useEffect(function () {\n    var unsubscribe = broadcast.receive(function () {\n      return __NEXTAUTH._getSession({\n        event: \"storage\"\n      });\n    });\n    return function () {\n      return unsubscribe();\n    };\n  }, []);\n  React.useEffect(function () {\n    var _props$refetchOnWindo = props.refetchOnWindowFocus,\n      refetchOnWindowFocus = _props$refetchOnWindo === void 0 ? true : _props$refetchOnWindo;\n    var visibilityHandler = function visibilityHandler() {\n      if (refetchOnWindowFocus && document.visibilityState === \"visible\") __NEXTAUTH._getSession({\n        event: \"visibilitychange\"\n      });\n    };\n    document.addEventListener(\"visibilitychange\", visibilityHandler, false);\n    return function () {\n      return document.removeEventListener(\"visibilitychange\", visibilityHandler, false);\n    };\n  }, [props.refetchOnWindowFocus]);\n  var isOnline = useOnline();\n  var shouldRefetch = refetchWhenOffline !== false || isOnline;\n  React.useEffect(function () {\n    if (refetchInterval && shouldRefetch) {\n      var refetchIntervalTimer = setInterval(function () {\n        if (__NEXTAUTH._session) {\n          __NEXTAUTH._getSession({\n            event: \"poll\"\n          });\n        }\n      }, refetchInterval * 1000);\n      return function () {\n        return clearInterval(refetchIntervalTimer);\n      };\n    }\n  }, [refetchInterval, shouldRefetch]);\n  var value = React.useMemo(function () {\n    return {\n      data: session,\n      status: loading ? \"loading\" : session ? \"authenticated\" : \"unauthenticated\",\n      update: function update(data) {\n        return (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee2() {\n          var newSession;\n          return _regenerator.default.wrap(function _callee2$(_context2) {\n            while (1) switch (_context2.prev = _context2.next) {\n              case 0:\n                if (!(loading || !session)) {\n                  _context2.next = 2;\n                  break;\n                }\n                return _context2.abrupt(\"return\");\n              case 2:\n                setLoading(true);\n                _context2.t0 = _utils.fetchData;\n                _context2.t1 = __NEXTAUTH;\n                _context2.t2 = logger;\n                _context2.next = 8;\n                return getCsrfToken();\n              case 8:\n                _context2.t3 = _context2.sent;\n                _context2.t4 = data;\n                _context2.t5 = {\n                  csrfToken: _context2.t3,\n                  data: _context2.t4\n                };\n                _context2.t6 = {\n                  body: _context2.t5\n                };\n                _context2.t7 = {\n                  req: _context2.t6\n                };\n                _context2.next = 15;\n                return (0, _context2.t0)(\"session\", _context2.t1, _context2.t2, _context2.t7);\n              case 15:\n                newSession = _context2.sent;\n                setLoading(false);\n                if (newSession) {\n                  setSession(newSession);\n                  broadcast.post({\n                    event: \"session\",\n                    data: {\n                      trigger: \"getSession\"\n                    }\n                  });\n                }\n                return _context2.abrupt(\"return\", newSession);\n              case 19:\n              case \"end\":\n                return _context2.stop();\n            }\n          }, _callee2);\n        }))();\n      }\n    };\n  }, [session, loading]);\n  return (0, _jsxRuntime.jsx)(SessionContext.Provider, {\n    value: value,\n    children: children\n  });\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-auth/react/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-auth/react/types.js":
/*!***********************************************!*\
  !*** ./node_modules/next-auth/react/types.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL3JlYWN0L3R5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3Zpc2lvbmxlbnMvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL3JlYWN0L3R5cGVzLmpzP2NjMDciXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICB2YWx1ZTogdHJ1ZVxufSk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-auth/react/types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-auth/utils/logger.js":
/*!************************************************!*\
  !*** ./node_modules/next-auth/utils/logger.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(ssr)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = void 0;\nexports.proxyLogger = proxyLogger;\nexports.setLogger = setLogger;\nvar _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ \"(ssr)/./node_modules/@babel/runtime/regenerator/index.js\"));\nvar _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/defineProperty.js\"));\nvar _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ \"(ssr)/./node_modules/@babel/runtime/helpers/asyncToGenerator.js\"));\nvar _errors = __webpack_require__(/*! ../core/errors */ \"(ssr)/./node_modules/next-auth/core/errors.js\");\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2.default)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction formatError(o) {\n  if (o instanceof Error && !(o instanceof _errors.UnknownError)) {\n    return {\n      message: o.message,\n      stack: o.stack,\n      name: o.name\n    };\n  }\n  if (hasErrorProperty(o)) {\n    var _o$message;\n    o.error = formatError(o.error);\n    o.message = (_o$message = o.message) !== null && _o$message !== void 0 ? _o$message : o.error.message;\n  }\n  return o;\n}\nfunction hasErrorProperty(x) {\n  return !!(x !== null && x !== void 0 && x.error);\n}\nvar _logger = {\n  error: function error(code, metadata) {\n    metadata = formatError(metadata);\n    console.error(\"[next-auth][error][\".concat(code, \"]\"), \"\\nhttps://next-auth.js.org/errors#\".concat(code.toLowerCase()), metadata.message, metadata);\n  },\n  warn: function warn(code) {\n    console.warn(\"[next-auth][warn][\".concat(code, \"]\"), \"\\nhttps://next-auth.js.org/warnings#\".concat(code.toLowerCase()));\n  },\n  debug: function debug(code, metadata) {\n    console.log(\"[next-auth][debug][\".concat(code, \"]\"), metadata);\n  }\n};\nfunction setLogger() {\n  var newLogger = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var debug = arguments.length > 1 ? arguments[1] : undefined;\n  if (!debug) _logger.debug = function () {};\n  if (newLogger.error) _logger.error = newLogger.error;\n  if (newLogger.warn) _logger.warn = newLogger.warn;\n  if (newLogger.debug) _logger.debug = newLogger.debug;\n}\nvar _default = exports[\"default\"] = _logger;\nfunction proxyLogger() {\n  var logger = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : _logger;\n  var basePath = arguments.length > 1 ? arguments[1] : undefined;\n  try {\n    if (typeof window === \"undefined\") {\n      return logger;\n    }\n    var clientLogger = {};\n    var _loop = function _loop(level) {\n      clientLogger[level] = function () {\n        var _ref = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee(code, metadata) {\n          var url, body;\n          return _regenerator.default.wrap(function _callee$(_context) {\n            while (1) switch (_context.prev = _context.next) {\n              case 0:\n                _logger[level](code, metadata);\n                if (level === \"error\") {\n                  metadata = formatError(metadata);\n                }\n                ;\n                metadata.client = true;\n                url = \"\".concat(basePath, \"/_log\");\n                body = new URLSearchParams(_objectSpread({\n                  level: level,\n                  code: code\n                }, metadata));\n                if (!navigator.sendBeacon) {\n                  _context.next = 8;\n                  break;\n                }\n                return _context.abrupt(\"return\", navigator.sendBeacon(url, body));\n              case 8:\n                _context.next = 10;\n                return fetch(url, {\n                  method: \"POST\",\n                  body: body,\n                  keepalive: true\n                });\n              case 10:\n                return _context.abrupt(\"return\", _context.sent);\n              case 11:\n              case \"end\":\n                return _context.stop();\n            }\n          }, _callee);\n        }));\n        return function (_x, _x2) {\n          return _ref.apply(this, arguments);\n        };\n      }();\n    };\n    for (var level in logger) {\n      _loop(level);\n    }\n    return clientLogger;\n  } catch (_unused) {\n    return _logger;\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-auth/utils/logger.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-auth/utils/parse-url.js":
/*!***************************************************!*\
  !*** ./node_modules/next-auth/utils/parse-url.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = parseUrl;\nfunction parseUrl(url) {\n  var _url2;\n  const defaultUrl = new URL(\"http://localhost:3000/api/auth\");\n  if (url && !url.startsWith(\"http\")) {\n    url = `https://${url}`;\n  }\n  const _url = new URL((_url2 = url) !== null && _url2 !== void 0 ? _url2 : defaultUrl);\n  const path = (_url.pathname === \"/\" ? defaultUrl.pathname : _url.pathname).replace(/\\/$/, \"\");\n  const base = `${_url.origin}${path}`;\n  return {\n    origin: _url.origin,\n    host: _url.host,\n    path,\n    base,\n    toString: () => base\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL3V0aWxzL3BhcnNlLXVybC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkM7QUFDN0M7QUFDQSxDQUFDLEVBQUM7QUFDRixrQkFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLElBQUk7QUFDekI7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLFlBQVksRUFBRSxLQUFLO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly92aXNpb25sZW5zLy4vbm9kZV9tb2R1bGVzL25leHQtYXV0aC91dGlscy9wYXJzZS11cmwuanM/ZmM2MCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gIHZhbHVlOiB0cnVlXG59KTtcbmV4cG9ydHMuZGVmYXVsdCA9IHBhcnNlVXJsO1xuZnVuY3Rpb24gcGFyc2VVcmwodXJsKSB7XG4gIHZhciBfdXJsMjtcbiAgY29uc3QgZGVmYXVsdFVybCA9IG5ldyBVUkwoXCJodHRwOi8vbG9jYWxob3N0OjMwMDAvYXBpL2F1dGhcIik7XG4gIGlmICh1cmwgJiYgIXVybC5zdGFydHNXaXRoKFwiaHR0cFwiKSkge1xuICAgIHVybCA9IGBodHRwczovLyR7dXJsfWA7XG4gIH1cbiAgY29uc3QgX3VybCA9IG5ldyBVUkwoKF91cmwyID0gdXJsKSAhPT0gbnVsbCAmJiBfdXJsMiAhPT0gdm9pZCAwID8gX3VybDIgOiBkZWZhdWx0VXJsKTtcbiAgY29uc3QgcGF0aCA9IChfdXJsLnBhdGhuYW1lID09PSBcIi9cIiA/IGRlZmF1bHRVcmwucGF0aG5hbWUgOiBfdXJsLnBhdGhuYW1lKS5yZXBsYWNlKC9cXC8kLywgXCJcIik7XG4gIGNvbnN0IGJhc2UgPSBgJHtfdXJsLm9yaWdpbn0ke3BhdGh9YDtcbiAgcmV0dXJuIHtcbiAgICBvcmlnaW46IF91cmwub3JpZ2luLFxuICAgIGhvc3Q6IF91cmwuaG9zdCxcbiAgICBwYXRoLFxuICAgIGJhc2UsXG4gICAgdG9TdHJpbmc6ICgpID0+IGJhc2VcbiAgfTtcbn0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-auth/utils/parse-url.js\n");

/***/ })

};
;