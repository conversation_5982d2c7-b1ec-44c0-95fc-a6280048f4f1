// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// User Management
model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  phone         String?
  role          String    @default("CUSTOMER")
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  accounts Account[]
  sessions Session[]
  orders   Order[]
  reviews  Review[]
  addresses Address[]
  wishlist WishlistItem[]

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verificationtokens")
}

// Product Management
model Category {
  id          String    @id @default(cuid())
  name        String    @unique
  slug        String    @unique
  description String?
  image       String?
  parentId    String?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  parent   Category?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children Category[] @relation("CategoryHierarchy")
  products Product[]

  @@map("categories")
}

model Brand {
  id          String    @id @default(cuid())
  name        String    @unique
  slug        String    @unique
  description String?
  logo        String?
  website     String?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  products Product[]

  @@map("brands")
}

model Product {
  id          String      @id @default(cuid())
  name        String
  slug        String      @unique
  description String?
  shortDesc   String?
  sku         String      @unique
  price       Float
  comparePrice Float?
  cost        Float?
  trackQuantity Boolean   @default(true)
  quantity    Int         @default(0)
  minQuantity Int         @default(0)
  weight      Float?
  dimensions  String?
  status      String        @default("DRAFT")
  isActive    Boolean     @default(true)
  isFeatured  Boolean     @default(false)
  tags        String?
  metaTitle   String?
  metaDesc    String?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  categoryId String
  category   Category @relation(fields: [categoryId], references: [id])
  brandId    String?
  brand      Brand?   @relation(fields: [brandId], references: [id])

  images       ProductImage[]
  variants     ProductVariant[]
  orderItems   OrderItem[]
  reviews      Review[]
  wishlistItems WishlistItem[]

  @@map("products")
}

model ProductImage {
  id        String   @id @default(cuid())
  url       String
  alt       String?
  position  Int      @default(0)
  createdAt DateTime @default(now())

  // Relations
  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_images")
}

model ProductVariant {
  id        String   @id @default(cuid())
  name      String
  value     String
  price     Float?
  sku       String?
  quantity  Int      @default(0)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_variants")
}

// Order Management
model Order {
  id              String      @id @default(cuid())
  orderNumber     String      @unique
  status          String @default("PENDING")
  paymentStatus   String @default("PENDING")
  shippingStatus  String @default("PENDING")
  subtotal        Float
  tax             Float       @default(0)
  shipping        Float       @default(0)
  discount        Float       @default(0)
  total           Float
  currency        String      @default("USD")
  notes           String?
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  // Relations
  userId String
  user   User   @relation(fields: [userId], references: [id])

  items           OrderItem[]
  shippingAddress Address?    @relation("ShippingAddress", fields: [shippingAddressId], references: [id])
  billingAddress  Address?    @relation("BillingAddress", fields: [billingAddressId], references: [id])
  shippingAddressId String?
  billingAddressId  String?
  payments        Payment[]

  @@map("orders")
}

model OrderItem {
  id        String   @id @default(cuid())
  quantity  Int
  price     Float
  total     Float
  createdAt DateTime @default(now())

  // Relations
  orderId   String
  order     Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  productId String
  product   Product @relation(fields: [productId], references: [id])

  @@map("order_items")
}

model Address {
  id          String   @id @default(cuid())
  firstName   String
  lastName    String
  company     String?
  address1    String
  address2    String?
  city        String
  state       String
  zipCode     String
  country     String
  phone       String?
  isDefault   Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  shippingOrders Order[] @relation("ShippingAddress")
  billingOrders  Order[] @relation("BillingAddress")

  @@map("addresses")
}

model Payment {
  id              String        @id @default(cuid())
  amount          Float
  currency        String        @default("USD")
  status          String @default("PENDING")
  method          String
  transactionId   String?
  gatewayResponse String?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  orderId String
  order   Order  @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@map("payments")
}

// Reviews and Ratings
model Review {
  id        String   @id @default(cuid())
  rating    Int      // 1-5 stars
  title     String?
  comment   String?
  isVerified Boolean @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  userId    String
  user      User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
  @@map("reviews")
}

// Wishlist
model WishlistItem {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())

  // Relations
  userId    String
  user      User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
  @@map("wishlist_items")
}

// Note: SQLite doesn't support enums, so we use String fields with default values
// Valid values for reference:
// UserRole: CUSTOMER, ADMIN, MANAGER, STAFF
// ProductStatus: DRAFT, ACTIVE, INACTIVE, ARCHIVED
// OrderStatus: PENDING, CONFIRMED, PROCESSING, SHIPPED, DELIVERED, CANCELLED, REFUNDED
// PaymentStatus: PENDING, PAID, FAILED, REFUNDED, PARTIALLY_REFUNDED
// ShippingStatus: PENDING, PROCESSING, SHIPPED, IN_TRANSIT, DELIVERED, RETURNED
// PaymentMethod: CREDIT_CARD, DEBIT_CARD, PAYPAL, STRIPE, CASH_ON_DELIVERY, BANK_TRANSFER
