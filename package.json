{"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "Professional e-commerce platform for contact lenses and medical glasses", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^5.6.0", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-toast": "^1.1.5", "@stripe/stripe-js": "^2.1.11", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "lucide-react": "^0.294.0", "next": "^14.0.0", "next-auth": "^4.24.5", "next-themes": "^0.4.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "recharts": "^2.8.0", "stripe": "^14.7.0", "tailwind-merge": "^2.0.0", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@types/node": "^20.9.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-config-next": "^14.0.0", "postcss": "^8.4.31", "prisma": "^5.6.0", "tailwindcss": "^3.3.5", "tsx": "^4.1.0", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0"}}