import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seeding...')

  // Create categories
  const dailyLenses = await prisma.category.create({
    data: {
      name: 'Daily Contact Lenses',
      slug: 'daily-contact-lenses',
      description: 'Fresh, comfortable lenses for everyday use',
      image: 'https://images.unsplash.com/photo-1574258495973-f010dfbb5371?w=400&h=400&fit=crop',
      isActive: true,
    },
  })

  const monthlyLenses = await prisma.category.create({
    data: {
      name: 'Monthly Contact Lenses',
      slug: 'monthly-contact-lenses',
      description: 'Long-lasting comfort for extended wear',
      image: 'https://images.unsplash.com/photo-**********-38febf6782e7?w=400&h=400&fit=crop',
      isActive: true,
    },
  })

  const coloredLenses = await prisma.category.create({
    data: {
      name: 'Colored Contact Lenses',
      slug: 'colored-contact-lenses',
      description: 'Transform your look with vibrant colors',
      image: 'https://images.unsplash.com/photo-1598300042247-d088f8ab3a91?w=400&h=400&fit=crop',
      isActive: true,
    },
  })

  const medicalGlasses = await prisma.category.create({
    data: {
      name: 'Medical Glasses',
      slug: 'medical-glasses',
      description: 'Prescription eyewear for clear vision',
      image: 'https://images.unsplash.com/photo-1511499767150-a48a237f0083?w=400&h=400&fit=crop',
      isActive: true,
    },
  })

  // Create brands
  const johnsonAndJohnson = await prisma.brand.create({
    data: {
      name: 'Johnson & Johnson',
      slug: 'johnson-johnson',
      description: 'Leading manufacturer of contact lenses',
      logo: 'https://via.placeholder.com/200x100/0ea5e9/ffffff?text=J%26J',
      website: 'https://www.jnjvisioncare.com',
      isActive: true,
    },
  })

  const cooperVision = await prisma.brand.create({
    data: {
      name: 'CooperVision',
      slug: 'coopervision',
      description: 'Innovative contact lens solutions',
      logo: 'https://via.placeholder.com/200x100/0ea5e9/ffffff?text=CooperVision',
      website: 'https://coopervision.com',
      isActive: true,
    },
  })

  const rayBan = await prisma.brand.create({
    data: {
      name: 'Ray-Ban',
      slug: 'ray-ban',
      description: 'Iconic eyewear brand',
      logo: 'https://via.placeholder.com/200x100/0ea5e9/ffffff?text=Ray-Ban',
      website: 'https://www.ray-ban.com',
      isActive: true,
    },
  })

  // Create products
  const acuvueOasys = await prisma.product.create({
    data: {
      name: 'Acuvue Oasys Daily',
      slug: 'acuvue-oasys-daily',
      description: 'Experience exceptional comfort with ACUVUE OASYS 1-Day contact lenses. These daily disposable lenses feature HydraLuxe Technology for all-day comfort.',
      shortDesc: 'Daily disposable contact lenses with HydraLuxe Technology',
      sku: 'AOD001',
      price: 29.99,
      comparePrice: 39.99,
      cost: 15.00,
      trackQuantity: true,
      quantity: 100,
      minQuantity: 10,
      weight: 0.1,
      status: "ACTIVE",
      isActive: true,
      isFeatured: true,
      tags: 'daily, comfort, hydraluxe',
      metaTitle: 'Acuvue Oasys Daily Contact Lenses - VisionLens',
      metaDesc: 'Shop Acuvue Oasys Daily contact lenses with HydraLuxe Technology for exceptional all-day comfort.',
      categoryId: dailyLenses.id,
      brandId: johnsonAndJohnson.id,
    },
  })

  const biofinity = await prisma.product.create({
    data: {
      name: 'Biofinity Monthly',
      slug: 'biofinity-monthly',
      description: 'Biofinity contact lenses combine advanced materials and lens design to bring you an ultra-soft, comfortable lens that stays moist and comfortable all day long.',
      shortDesc: 'Monthly silicone hydrogel contact lenses',
      sku: 'BIO001',
      price: 45.99,
      cost: 22.00,
      trackQuantity: true,
      quantity: 75,
      minQuantity: 5,
      weight: 0.1,
      status: "ACTIVE",
      isActive: true,
      isFeatured: true,
      tags: 'monthly, silicone hydrogel, breathable',
      metaTitle: 'Biofinity Monthly Contact Lenses - VisionLens',
      metaDesc: 'Shop Biofinity monthly contact lenses with advanced silicone hydrogel material for superior comfort.',
      categoryId: monthlyLenses.id,
      brandId: cooperVision.id,
    },
  })

  const rayBanGlasses = await prisma.product.create({
    data: {
      name: 'Ray-Ban Prescription Glasses',
      slug: 'ray-ban-prescription-glasses',
      description: 'Classic Ray-Ban frames available with prescription lenses. Choose from a variety of frame styles and lens options.',
      shortDesc: 'Classic prescription eyewear with iconic Ray-Ban style',
      sku: 'RBG001',
      price: 159.99,
      comparePrice: 199.99,
      cost: 80.00,
      trackQuantity: true,
      quantity: 50,
      minQuantity: 5,
      weight: 0.3,
      status: "ACTIVE",
      isActive: true,
      isFeatured: true,
      tags: 'prescription, glasses, classic, style',
      metaTitle: 'Ray-Ban Prescription Glasses - VisionLens',
      metaDesc: 'Shop authentic Ray-Ban prescription glasses with classic frames and high-quality lenses.',
      categoryId: medicalGlasses.id,
      brandId: rayBan.id,
    },
  })

  // Add product images
  await prisma.productImage.createMany({
    data: [
      {
        productId: acuvueOasys.id,
        url: 'https://images.unsplash.com/photo-1574258495973-f010dfbb5371?w=600&h=600&fit=crop',
        alt: 'Acuvue Oasys Daily Contact Lenses',
        position: 0,
      },
      {
        productId: biofinity.id,
        url: 'https://images.unsplash.com/photo-**********-38febf6782e7?w=600&h=600&fit=crop',
        alt: 'Biofinity Monthly Contact Lenses',
        position: 0,
      },
      {
        productId: rayBanGlasses.id,
        url: 'https://images.unsplash.com/photo-1511499767150-a48a237f0083?w=600&h=600&fit=crop',
        alt: 'Ray-Ban Prescription Glasses',
        position: 0,
      },
    ],
  })

  // Create admin user
  await prisma.user.create({
    data: {
      name: 'Admin User',
      email: '<EMAIL>',
      role: "ADMIN",
    },
  })

  console.log('✅ Database seeding completed successfully!')
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
